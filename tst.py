import pygame
import os
import json
import sys

# --- Game Constants ---
SCREEN_WIDTH, SCREEN_HEIGHT = 1024, 768
EDIT_MODE_GRID_SIZE = 40
LEVEL_FILENAME = "level_data.json"
GRID_COLOR = (80, 80, 80)

# Game States
GAME_STATE_MENU = 0
GAME_STATE_PLAYING = 1
GAME_STATE_EDITOR = 2
GAME_STATE_PAUSED = 3
GAME_STATE_LORE = 4

# Object Types with improved organization
OBJECT_TYPES = [
    {'name': 'platforma_1x1', 'color': (0, 180, 0), 'size': (1, 1), 'type': 'platforma'},
    {'name': 'platforma_4x1', 'color': (0, 150, 0), 'size': (4, 1), 'type': 'platforma'},
    {'name': 'platforma_1x4', 'color': (0, 120, 0), 'size': (1, 4), 'type': 'platforma'},
    {'name': 'bodaky', 'color': (180, 180, 190), 'size': (1, 1), 'type': 'past'},
    {'name': 'past_ohen', 'color': (255, 100, 0), 'size': (1, 1), 'type': 'past'},
    {'name': 'koule', 'color': (100, 100, 100), 'size': (1, 1), 'type': 'koule'},
    {'name': 'checkpoint', 'color': (255, 255, 0), 'activated_color': (50, 255, 50), 'size': (1, 1), 'type': 'checkpoint'},
    {'name': 'enemy_walker', 'color': (255, 50, 50), 'size': (1, 1), 'type': 'enemy'},
    {'name': 'collectible', 'color': (255, 215, 0), 'size': (1, 1), 'type': 'collectible'},
    {'name': 'moving_platform', 'color': (100, 255, 100), 'size': (2, 1), 'type': 'moving_platform'},
    {'name': 'goal', 'color': (0, 255, 255), 'size': (1, 2), 'type': 'goal'}
]

# Game Lore
GAME_LORE = {
    'title': 'Light or Dead',
    'intro': 'In a world consumed by darkness, you are the last beacon of hope. Navigate through treacherous platforms, avoid deadly traps, and collect the ancient light crystals to restore balance to the realm.',
    'story_fragments': [
        'The Great Eclipse has plunged the world into eternal night...',
        'Ancient mechanisms still function, waiting for the chosen one...',
        'Each crystal you collect weakens the shadow\'s grip on reality...',
        'The path ahead grows more dangerous, but hope remains...'
    ]
}

# --- Game Classes ---
class Camera:
    def __init__(self, width, height):
        self.camera_rect = pygame.Rect(0, 0, width, height)
        self.width = width
        self.height = height

    def apply(self, entity_rect):
        return entity_rect.move(self.camera_rect.topleft)

    def update(self, target_rect):
        x = -target_rect.centerx + int(self.width/2)
        y = -target_rect.centery + int(self.height/2)
        self.camera_rect.topleft = (x, y)

class GameState:
    def __init__(self):
        self.current_state = GAME_STATE_MENU
        self.paused = False
        self.level_completed = False
        self.collectibles_found = 0
        self.total_collectibles = 0
        self.current_lore_fragment = 0

    def set_state(self, new_state):
        self.current_state = new_state

    def toggle_pause(self):
        if self.current_state == GAME_STATE_PLAYING:
            self.current_state = GAME_STATE_PAUSED
            self.paused = True
        elif self.current_state == GAME_STATE_PAUSED:
            self.current_state = GAME_STATE_PLAYING
            self.paused = False

class Player:
    def __init__(self, x, y):
        self.rect = pygame.Rect(x, y, 40, 60)
        self.velocity = pygame.math.Vector2(0, 0)
        self.base_speed = 3.5
        self.sprint_bonus = 2
        self.gravity = 0.5
        self.jump_force = -12
        self.on_ground = False
        self.wall_jump_y_force = -11
        self.wall_jump_x_speed = 7
        self.can_wall_jump = False
        self.has_wall_jumped = False
        self.wall_jump_direction = 0
        self.start_pos = (x, y)
        self.spawn_point = (x, y)

    def reset_to_spawn(self):
        self.rect.topleft = self.spawn_point
        self.velocity.x, self.velocity.y = 0, 0
        self.on_ground = False
        self.has_wall_jumped = False

    def set_spawn_point(self, x, y):
        self.spawn_point = (x, y)

# --- Level Management Functions ---
def load_level(filename):
    if not os.path.exists(filename):
        print(f"File '{filename}' not found, using default demo level.")
        return create_demo_level()

    print(f"Loading level from file '{filename}'...")
    try:
        with open(filename, 'r') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        print(f"Error reading file '{filename}'. Returning demo level.")
        return create_demo_level()

def create_demo_level():
    """Creates a complete demo level that showcases all game mechanics"""
    return [
        # Starting platform
        {'rect_data': [100, 500, 160, 40], 'obj_name': 'platforma_4x1'},
        # Tutorial platforms
        {'rect_data': [300, 450, 40, 40], 'obj_name': 'platforma_1x1'},
        {'rect_data': [400, 400, 40, 40], 'obj_name': 'platforma_1x1'},
        # First collectible
        {'rect_data': [420, 360, 40, 40], 'obj_name': 'collectible'},
        # Wall jump section
        {'rect_data': [600, 340, 40, 200], 'obj_name': 'platforma_1x4'},
        {'rect_data': [700, 280, 40, 40], 'obj_name': 'platforma_1x1'},
        # Checkpoint
        {'rect_data': [720, 240, 40, 40], 'obj_name': 'checkpoint'},
        # Trap section
        {'rect_data': [800, 280, 40, 40], 'obj_name': 'bodaky'},
        {'rect_data': [840, 320, 40, 40], 'obj_name': 'past_ohen'},
        # Moving platform area
        {'rect_data': [900, 400, 80, 40], 'obj_name': 'moving_platform'},
        # Enemy section
        {'rect_data': [1100, 450, 160, 40], 'obj_name': 'platforma_4x1'},
        {'rect_data': [1120, 410, 40, 40], 'obj_name': 'enemy_walker'},
        # More collectibles
        {'rect_data': [1200, 380, 40, 40], 'obj_name': 'collectible'},
        {'rect_data': [1300, 350, 40, 40], 'obj_name': 'collectible'},
        # Final challenge with koule
        {'rect_data': [1400, 440, 160, 40], 'obj_name': 'platforma_4x1'},
        {'rect_data': [1420, 400, 40, 40], 'obj_name': 'koule'},
        # Final checkpoint
        {'rect_data': [1600, 400, 40, 40], 'obj_name': 'checkpoint'},
        # Goal
        {'rect_data': [1700, 360, 40, 80], 'obj_name': 'goal'},
        # Ground platforms
        {'rect_data': [-500, 540, 3000, 60], 'obj_name': 'platforma_4x1'}
    ]

def save_level(filename, level_objects):
    save_data = [
        {
            'rect_data': [obj['rect'].x, obj['rect'].y, obj['rect'].width, obj['rect'].height],
            'obj_name': obj['obj_name']
        }
        for obj in level_objects
    ]
    with open(filename, 'w') as f:
        json.dump(save_data, f, indent=4)
    print(f"Level saved to file '{filename}'!")

# --- Menu System ---
class Menu:
    def __init__(self, screen_width, screen_height):
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.font_large = pygame.font.Font(None, 72)
        self.font_medium = pygame.font.Font(None, 48)
        self.font_small = pygame.font.Font(None, 36)
        self.selected_option = 0
        self.menu_options = ['New Game', 'Continue', 'Level Editor', 'View Lore', 'Exit']
        self.pause_options = ['Resume', 'Restart Level', 'Main Menu', 'Exit']

    def handle_input(self, event, current_state):
        if event.type == pygame.KEYDOWN:
            if current_state == GAME_STATE_MENU:
                if event.key == pygame.K_UP:
                    self.selected_option = (self.selected_option - 1) % len(self.menu_options)
                elif event.key == pygame.K_DOWN:
                    self.selected_option = (self.selected_option + 1) % len(self.menu_options)
                elif event.key == pygame.K_RETURN:
                    return self.menu_options[self.selected_option]
            elif current_state == GAME_STATE_PAUSED:
                if event.key == pygame.K_UP:
                    self.selected_option = (self.selected_option - 1) % len(self.pause_options)
                elif event.key == pygame.K_DOWN:
                    self.selected_option = (self.selected_option + 1) % len(self.pause_options)
                elif event.key == pygame.K_RETURN:
                    return self.pause_options[self.selected_option]
        return None

    def draw_main_menu(self, screen):
        screen.fill((20, 20, 40))

        # Title
        title_text = self.font_large.render(GAME_LORE['title'], True, (255, 255, 255))
        title_rect = title_text.get_rect(center=(self.screen_width//2, 150))
        screen.blit(title_text, title_rect)

        # Subtitle
        subtitle_text = self.font_small.render("Navigate the darkness, collect the light", True, (200, 200, 200))
        subtitle_rect = subtitle_text.get_rect(center=(self.screen_width//2, 200))
        screen.blit(subtitle_text, subtitle_rect)

        # Menu options
        for i, option in enumerate(self.menu_options):
            color = (255, 255, 0) if i == self.selected_option else (255, 255, 255)
            option_text = self.font_medium.render(option, True, color)
            option_rect = option_text.get_rect(center=(self.screen_width//2, 300 + i * 60))
            screen.blit(option_text, option_rect)

        # Controls info
        controls_text = self.font_small.render("Use Arrow Keys to navigate, Enter to select", True, (150, 150, 150))
        controls_rect = controls_text.get_rect(center=(self.screen_width//2, self.screen_height - 50))
        screen.blit(controls_text, controls_rect)

    def draw_pause_menu(self, screen):
        # Semi-transparent overlay
        overlay = pygame.Surface((self.screen_width, self.screen_height))
        overlay.set_alpha(128)
        overlay.fill((0, 0, 0))
        screen.blit(overlay, (0, 0))

        # Pause menu box
        menu_width, menu_height = 400, 300
        menu_x = (self.screen_width - menu_width) // 2
        menu_y = (self.screen_height - menu_height) // 2

        pygame.draw.rect(screen, (40, 40, 60), (menu_x, menu_y, menu_width, menu_height))
        pygame.draw.rect(screen, (255, 255, 255), (menu_x, menu_y, menu_width, menu_height), 3)

        # Pause title
        pause_text = self.font_medium.render("PAUSED", True, (255, 255, 255))
        pause_rect = pause_text.get_rect(center=(self.screen_width//2, menu_y + 50))
        screen.blit(pause_text, pause_rect)

        # Pause options
        for i, option in enumerate(self.pause_options):
            color = (255, 255, 0) if i == self.selected_option else (255, 255, 255)
            option_text = self.font_small.render(option, True, color)
            option_rect = option_text.get_rect(center=(self.screen_width//2, menu_y + 120 + i * 40))
            screen.blit(option_text, option_rect)

# --- Utility Functions ---
def draw_grid(surface, camera):
    screen_width, screen_height = surface.get_size()
    offset_x = camera.camera_rect.x % EDIT_MODE_GRID_SIZE
    offset_y = camera.camera_rect.y % EDIT_MODE_GRID_SIZE

    for x in range(0, screen_width + EDIT_MODE_GRID_SIZE, EDIT_MODE_GRID_SIZE):
        pygame.draw.line(surface, GRID_COLOR, (x + offset_x, 0), (x + offset_x, screen_height))

    for y in range(0, screen_height + EDIT_MODE_GRID_SIZE, EDIT_MODE_GRID_SIZE):
        pygame.draw.line(surface, GRID_COLOR, (0, y + offset_y), (screen_width, y + offset_y))

# --- Lore Display System ---
class LoreDisplay:
    def __init__(self, screen_width, screen_height):
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.font_large = pygame.font.Font(None, 64)
        self.font_medium = pygame.font.Font(None, 42)
        self.font_small = pygame.font.Font(None, 32)
        self.current_fragment = 0
        self.scroll_y = 0

    def handle_input(self, event):
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                return "back_to_menu"
            elif event.key == pygame.K_UP:
                self.scroll_y = max(0, self.scroll_y - 20)
            elif event.key == pygame.K_DOWN:
                self.scroll_y += 20
            elif event.key == pygame.K_LEFT:
                self.current_fragment = max(0, self.current_fragment - 1)
            elif event.key == pygame.K_RIGHT:
                self.current_fragment = min(len(GAME_LORE['story_fragments']) - 1, self.current_fragment + 1)
        return None

    def draw_lore(self, screen):
        screen.fill((10, 10, 30))

        # Title
        title_text = self.font_large.render("Game Lore", True, (255, 255, 255))
        title_rect = title_text.get_rect(center=(self.screen_width//2, 80))
        screen.blit(title_text, title_rect)

        # Main story intro
        y_offset = 150 - self.scroll_y
        intro_lines = self.wrap_text(GAME_LORE['intro'], self.font_medium, self.screen_width - 100)
        for line in intro_lines:
            if y_offset > -50 and y_offset < self.screen_height + 50:
                line_surface = self.font_medium.render(line, True, (200, 200, 200))
                line_rect = line_surface.get_rect(center=(self.screen_width//2, y_offset))
                screen.blit(line_surface, line_rect)
            y_offset += 50

        y_offset += 50

        # Story fragments
        fragment_title = self.font_medium.render(f"Chapter {self.current_fragment + 1}", True, (255, 255, 100))
        if y_offset > -50 and y_offset < self.screen_height + 50:
            fragment_title_rect = fragment_title.get_rect(center=(self.screen_width//2, y_offset))
            screen.blit(fragment_title, fragment_title_rect)
        y_offset += 60

        if self.current_fragment < len(GAME_LORE['story_fragments']):
            fragment_lines = self.wrap_text(GAME_LORE['story_fragments'][self.current_fragment], self.font_small, self.screen_width - 100)
            for line in fragment_lines:
                if y_offset > -50 and y_offset < self.screen_height + 50:
                    line_surface = self.font_small.render(line, True, (180, 180, 180))
                    line_rect = line_surface.get_rect(center=(self.screen_width//2, y_offset))
                    screen.blit(line_surface, line_rect)
                y_offset += 40

        # Navigation instructions
        nav_text = "Use Arrow Keys to navigate, ESC to return"
        nav_surface = self.font_small.render(nav_text, True, (150, 150, 150))
        nav_rect = nav_surface.get_rect(center=(self.screen_width//2, self.screen_height - 30))
        screen.blit(nav_surface, nav_rect)

        # Chapter indicator
        chapter_info = f"Chapter {self.current_fragment + 1} of {len(GAME_LORE['story_fragments'])}"
        chapter_surface = self.font_small.render(chapter_info, True, (150, 150, 150))
        chapter_rect = chapter_surface.get_rect(center=(self.screen_width//2, self.screen_height - 60))
        screen.blit(chapter_surface, chapter_rect)

    def wrap_text(self, text, font, max_width):
        words = text.split(' ')
        lines = []
        current_line = []

        for word in words:
            test_line = ' '.join(current_line + [word])
            if font.size(test_line)[0] <= max_width:
                current_line.append(word)
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                current_line = [word]

        if current_line:
            lines.append(' '.join(current_line))

        return lines

# --- Main Game Initialization ---
pygame.init()

screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("Light or Dead - Enhanced Edition")

# Initialize game objects
game_state = GameState()
player = Player(100, 450)
camera = Camera(SCREEN_WIDTH, SCREEN_HEIGHT)
menu = Menu(SCREEN_WIDTH, SCREEN_HEIGHT)
lore_display = LoreDisplay(SCREEN_WIDTH, SCREEN_HEIGHT)

# Level data
loaded_data = load_level(LEVEL_FILENAME)
level_objects = []
platform_colliders = []
trap_colliders = []
checkpoint_colliders = []
koule_objects = []
enemy_objects = []
collectible_objects = []
moving_platform_objects = []
goal_objects = []

def process_level_data(data):
    """Process level data and populate game object lists"""
    level_objects.clear()
    platform_colliders.clear()
    trap_colliders.clear()
    checkpoint_colliders.clear()
    koule_objects.clear()
    enemy_objects.clear()
    collectible_objects.clear()
    moving_platform_objects.clear()
    goal_objects.clear()

    checkpoint_id_counter = 0
    game_state.total_collectibles = 0

    for obj_data in data:
        obj_name = obj_data['obj_name']
        rect_data = obj_data['rect_data']
        obj_info = next((item for item in OBJECT_TYPES if item["name"] == obj_name), None)

        if not obj_info:
            print(f"Unknown object type '{obj_name}', skipping.")
            continue

        obj = {
            'rect': pygame.Rect(*rect_data),
            'obj_name': obj_name,
            'info': obj_info
        }

        # Sort objects into appropriate lists
        if obj_info['type'] == 'platforma':
            platform_colliders.append(obj['rect'])
        elif obj_info['type'] == 'past':
            trap_colliders.append(obj['rect'])
        elif obj_info['type'] == 'checkpoint':
            obj['id'] = checkpoint_id_counter
            checkpoint_id_counter += 1
            obj['activated'] = False
            checkpoint_colliders.append(obj)
        elif obj_info['type'] == 'koule':
            obj['active'] = False
            obj['speed'] = 5
            obj['direction'] = pygame.math.Vector2(1, 0)
            koule_objects.append(obj)
            trap_colliders.append(obj['rect'])
        elif obj_info['type'] == 'enemy':
            obj['direction'] = 1
            obj['speed'] = 2
            enemy_objects.append(obj)
            trap_colliders.append(obj['rect'])
        elif obj_info['type'] == 'collectible':
            obj['collected'] = False
            collectible_objects.append(obj)
            game_state.total_collectibles += 1
        elif obj_info['type'] == 'moving_platform':
            obj['start_pos'] = obj['rect'].center
            obj['direction'] = pygame.math.Vector2(1, 0)
            obj['speed'] = 1
            obj['distance'] = 100
            moving_platform_objects.append(obj)
            platform_colliders.append(obj['rect'])
        elif obj_info['type'] == 'goal':
            goal_objects.append(obj)

        level_objects.append(obj)

def reset_level():
    """Reset level to initial state"""
    player.rect.topleft = player.start_pos
    player.spawn_point = player.start_pos
    player.velocity.x, player.velocity.y = 0, 0
    game_state.collectibles_found = 0
    game_state.level_completed = False
    process_level_data(loaded_data)

def respawn_player():
    """Respawn player at current spawn point"""
    player.reset_to_spawn()

reset_level()

# Editor mode variables
edit_mode = False
current_object_index = 0
inventory_rect = pygame.Rect(10, 10, 60, SCREEN_HEIGHT - 20)
inventory_slots = []
slot_size = 50
slot_padding = 10

for i, obj_type in enumerate(OBJECT_TYPES):
    slot_y = inventory_rect.y + i * (slot_size + slot_padding) + slot_padding
    inventory_slots.append({
        'rect': pygame.Rect(inventory_rect.x + (inventory_rect.width - slot_size) / 2, slot_y, slot_size, slot_size),
        'info': obj_type
    })

# Game loop variables
running = True
clock = pygame.time.Clock()

# --- Main Game Loop ---
while running:
    clock.tick(60)
    mouse_pos_screen = pygame.mouse.get_pos()

    # Event handling
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False

        # Handle different game states
        if game_state.current_state == GAME_STATE_MENU:
            menu_action = menu.handle_input(event, game_state.current_state)
            if menu_action == 'New Game':
                game_state.set_state(GAME_STATE_PLAYING)
                reset_level()
            elif menu_action == 'Continue':
                game_state.set_state(GAME_STATE_PLAYING)
            elif menu_action == 'Level Editor':
                game_state.set_state(GAME_STATE_EDITOR)
                edit_mode = True
            elif menu_action == 'View Lore':
                game_state.set_state(GAME_STATE_LORE)
            elif menu_action == 'Exit':
                running = False

        elif game_state.current_state == GAME_STATE_LORE:
            lore_action = lore_display.handle_input(event)
            if lore_action == 'back_to_menu':
                game_state.set_state(GAME_STATE_MENU)
                menu.selected_option = 0

        elif game_state.current_state == GAME_STATE_PAUSED:
            pause_action = menu.handle_input(event, game_state.current_state)
            if pause_action == 'Resume':
                game_state.toggle_pause()
            elif pause_action == 'Restart Level':
                reset_level()
                game_state.set_state(GAME_STATE_PLAYING)
            elif pause_action == 'Main Menu':
                game_state.set_state(GAME_STATE_MENU)
                menu.selected_option = 0
            elif pause_action == 'Exit':
                running = False

        elif game_state.current_state == GAME_STATE_PLAYING:
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    game_state.toggle_pause()
                    menu.selected_option = 0
                elif event.key == pygame.K_r:
                    reset_level()
                elif event.key == pygame.K_SPACE:
                    if player.can_wall_jump and not player.has_wall_jumped:
                        player.velocity.y = player.wall_jump_y_force
                        player.velocity.x = player.wall_jump_x_speed * player.wall_jump_direction
                        player.has_wall_jumped = True
                        player.can_wall_jump = False

        elif game_state.current_state == GAME_STATE_EDITOR:
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_p:
                    game_state.set_state(GAME_STATE_PLAYING)
                    edit_mode = False
                    reset_level()
                elif event.key == pygame.K_s:
                    save_level(LEVEL_FILENAME, level_objects)
                elif event.key == pygame.K_ESCAPE:
                    game_state.set_state(GAME_STATE_MENU)
                    edit_mode = False
                    menu.selected_option = 0

            # Editor mouse handling
            if event.type == pygame.MOUSEBUTTONDOWN:
                clicked_on_inventory = False
                for i, slot in enumerate(inventory_slots):
                    if slot['rect'].collidepoint(mouse_pos_screen):
                        current_object_index = i
                        print(f"Selected object: {slot['info']['name']}")
                        clicked_on_inventory = True
                        break

                if not clicked_on_inventory:
                    mouse_pos_world = (
                        mouse_pos_screen[0] - camera.camera_rect.x,
                        mouse_pos_screen[1] - camera.camera_rect.y
                    )
                    if event.button == 1:  # Left click - place object
                        obj_info = OBJECT_TYPES[current_object_index]
                        size_w, size_h = obj_info['size']
                        width = size_w * EDIT_MODE_GRID_SIZE
                        height = size_h * EDIT_MODE_GRID_SIZE
                        snapped_x = (mouse_pos_world[0] // EDIT_MODE_GRID_SIZE) * EDIT_MODE_GRID_SIZE
                        snapped_y = (mouse_pos_world[1] // EDIT_MODE_GRID_SIZE) * EDIT_MODE_GRID_SIZE
                        new_obj = {
                            'rect': pygame.Rect(snapped_x, snapped_y, width, height),
                            'obj_name': obj_info['name']
                        }
                        if not any(obj['rect'] == new_obj['rect'] for obj in level_objects):
                            level_objects.append(new_obj)
                            process_level_data(level_objects)
                    elif event.button == 3:  # Right click - remove object
                        for obj in reversed(level_objects):
                            if obj['rect'].collidepoint(mouse_pos_world):
                                level_objects.remove(obj)
                                process_level_data(level_objects)
                                break

    # Game logic based on current state
    if game_state.current_state == GAME_STATE_PLAYING:
        # Player movement
        keys = pygame.key.get_pressed()
        velocity_x_intent = 0

        if keys[pygame.K_a] or keys[pygame.K_LEFT]:
            velocity_x_intent -= player.base_speed
        if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
            velocity_x_intent += player.base_speed

        # Sprint
        if keys[pygame.K_LSHIFT]:
            if velocity_x_intent < 0:
                velocity_x_intent -= player.sprint_bonus
            elif velocity_x_intent > 0:
                velocity_x_intent += player.sprint_bonus

        player.velocity.x = velocity_x_intent

        # Jump
        if keys[pygame.K_SPACE] and player.on_ground:
            player.velocity.y = player.jump_force
            player.on_ground = False
            player.has_wall_jumped = False

        # Apply gravity
        player.velocity.y += player.gravity
        if player.velocity.y > 15:
            player.velocity.y = 15

        # Horizontal movement and collision
        touching_wall = False
        player.rect.x += player.velocity.x

        for collider in platform_colliders:
            if player.rect.colliderect(collider):
                if not player.on_ground:
                    touching_wall = True
                    if player.velocity.x > 0:
                        player.wall_jump_direction = -1
                    elif player.velocity.x < 0:
                        player.wall_jump_direction = 1
                    else:
                        if player.rect.centerx < collider.centerx:
                            player.wall_jump_direction = -1
                        else:
                            player.wall_jump_direction = 1

                if player.velocity.x > 0:
                    player.rect.right = collider.left
                elif player.velocity.x < 0:
                    player.rect.left = collider.right

        player.can_wall_jump = touching_wall and not player.on_ground and not player.has_wall_jumped

        # Vertical movement and collision
        player.rect.y += player.velocity.y
        player.on_ground = False

        for collider in platform_colliders:
            if player.rect.colliderect(collider):
                if player.velocity.y > 0:
                    player.rect.bottom = collider.top
                    player.velocity.y = 0
                    player.on_ground = True
                    player.has_wall_jumped = False
                elif player.velocity.y < 0:
                    player.rect.top = collider.bottom
                    player.velocity.y = 0

        # Trap collision
        for trap_rect in trap_colliders:
            if player.rect.colliderect(trap_rect):
                respawn_player()
                break

        # Checkpoint collision
        for checkpoint in checkpoint_colliders:
            if not checkpoint.get('activated', False) and player.rect.colliderect(checkpoint['rect']):
                player.set_spawn_point(checkpoint['rect'].x, checkpoint['rect'].y)
                checkpoint['activated'] = True
                print(f"Checkpoint {checkpoint['id']} activated!")

                # Activate nearby koule objects
                for koule in koule_objects:
                    dist_x = abs(koule['rect'].centerx - checkpoint['rect'].centerx)
                    dist_y = abs(koule['rect'].centery - checkpoint['rect'].centery)
                    if dist_x < 5 * EDIT_MODE_GRID_SIZE and dist_y < 5 * EDIT_MODE_GRID_SIZE:
                        koule['active'] = True
                        print(f"Ball at {koule['rect'].topleft} activated!")

        # Collectible collision
        for collectible in collectible_objects:
            if not collectible['collected'] and player.rect.colliderect(collectible['rect']):
                collectible['collected'] = True
                game_state.collectibles_found += 1
                print(f"Collectible found! ({game_state.collectibles_found}/{game_state.total_collectibles})")

        # Goal collision
        for goal in goal_objects:
            if player.rect.colliderect(goal['rect']):
                if game_state.collectibles_found >= game_state.total_collectibles:
                    game_state.level_completed = True
                    print("Level completed! All collectibles found!")
                    # Could transition to victory screen or next level
                else:
                    print(f"Collect all crystals first! ({game_state.collectibles_found}/{game_state.total_collectibles})")

        # Update moving objects
        for koule in koule_objects:
            if koule['active']:
                koule['rect'].x += koule['speed'] * koule['direction'].x
                for platform in platform_colliders:
                    if koule['rect'].colliderect(platform):
                        koule['direction'].x *= -1
                        koule['rect'].x += koule['speed'] * koule['direction'].x
                        break

        # Update enemies
        for enemy in enemy_objects:
            enemy['rect'].x += enemy['speed'] * enemy['direction']
            # Simple AI: reverse direction when hitting platforms
            for platform in platform_colliders:
                if enemy['rect'].colliderect(platform):
                    enemy['direction'] *= -1
                    enemy['rect'].x += enemy['speed'] * enemy['direction']
                    break

        # Update moving platforms
        for moving_platform in moving_platform_objects:
            moving_platform['rect'].x += moving_platform['speed'] * moving_platform['direction'].x
            # Simple back-and-forth movement
            if abs(moving_platform['rect'].centerx - moving_platform['start_pos'][0]) > moving_platform['distance']:
                moving_platform['direction'].x *= -1

    elif game_state.current_state == GAME_STATE_EDITOR:
        # Editor camera movement
        keys = pygame.key.get_pressed()
        camera_speed = 5

        if keys[pygame.K_a] or keys[pygame.K_LEFT]:
            player.rect.x -= camera_speed
        if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
            player.rect.x += camera_speed
        if keys[pygame.K_w] or keys[pygame.K_UP]:
            player.rect.y -= camera_speed
        if keys[pygame.K_s] or keys[pygame.K_DOWN]:
            player.rect.y += camera_speed

    # Update camera
    camera.update(player.rect)

    # Rendering based on game state
    if game_state.current_state == GAME_STATE_MENU:
        menu.draw_main_menu(screen)

    elif game_state.current_state == GAME_STATE_LORE:
        lore_display.draw_lore(screen)

    elif game_state.current_state == GAME_STATE_PLAYING:
        # Game rendering
        screen.fill((30, 30, 50))  # Dark blue background

        # Draw level objects
        for obj in level_objects:
            info = obj['info']
            color = info['color']

            # Special rendering for different object types
            if info['type'] == 'checkpoint' and obj.get('activated', False):
                color = info['activated_color']
            elif info['type'] == 'collectible' and obj.get('collected', False):
                continue  # Don't draw collected items

            pygame.draw.rect(screen, color, camera.apply(obj['rect']))

        # Draw player
        player_color = (255, 0, 0)  # Red
        if player.can_wall_jump:
            player_color = (255, 255, 0)  # Yellow for wall jump
        elif player.on_ground:
            player_color = (255, 80, 80)  # Light red on ground

        pygame.draw.rect(screen, player_color, camera.apply(player.rect))

        # Draw UI elements
        font = pygame.font.Font(None, 36)

        # Collectibles counter
        collectible_text = f"Crystals: {game_state.collectibles_found}/{game_state.total_collectibles}"
        collectible_surface = font.render(collectible_text, True, (255, 255, 255))
        screen.blit(collectible_surface, (10, 10))

        # Level completion message
        if game_state.level_completed:
            victory_text = "LEVEL COMPLETED! Well done!"
            victory_surface = font.render(victory_text, True, (0, 255, 0))
            victory_rect = victory_surface.get_rect(center=(SCREEN_WIDTH//2, 100))
            screen.blit(victory_surface, victory_rect)

        # Controls help
        help_font = pygame.font.Font(None, 24)
        help_text = "WASD/Arrows: Move | Space: Jump/Wall Jump | R: Restart | ESC: Pause"
        help_surface = help_font.render(help_text, True, (200, 200, 200))
        screen.blit(help_surface, (10, SCREEN_HEIGHT - 30))

    elif game_state.current_state == GAME_STATE_EDITOR:
        # Editor rendering
        screen.fill((30, 30, 30))
        draw_grid(screen, camera)

        # Draw level objects
        for obj in level_objects:
            info = obj['info']
            color = info['color']
            if info['type'] == 'checkpoint' and obj.get('activated', False):
                color = info['activated_color']
            pygame.draw.rect(screen, color, camera.apply(obj['rect']))

        # Draw player (blue in editor mode)
        pygame.draw.rect(screen, (100, 100, 255), camera.apply(player.rect))

        # Draw ghost object
        selected_info = OBJECT_TYPES[current_object_index]
        ghost_w, ghost_h = selected_info['size']
        ghost_w *= EDIT_MODE_GRID_SIZE
        ghost_h *= EDIT_MODE_GRID_SIZE

        temp_ghost_surface = pygame.Surface((ghost_w, ghost_h), pygame.SRCALPHA)
        temp_ghost_surface.fill(selected_info['color'] + (128,))  # Color + alpha

        mouse_pos_world = (
            mouse_pos_screen[0] - camera.camera_rect.x,
            mouse_pos_screen[1] - camera.camera_rect.y
        )
        ghost_rect = pygame.Rect(
            (mouse_pos_world[0] // EDIT_MODE_GRID_SIZE) * EDIT_MODE_GRID_SIZE,
            (mouse_pos_world[1] // EDIT_MODE_GRID_SIZE) * EDIT_MODE_GRID_SIZE,
            ghost_w, ghost_h
        )
        screen.blit(temp_ghost_surface, camera.apply(ghost_rect))

        # Draw inventory
        pygame.draw.rect(screen, (20, 20, 20), inventory_rect)
        for i, slot in enumerate(inventory_slots):
            pygame.draw.rect(screen, slot['info']['color'], slot['rect'])
            if i == current_object_index:
                pygame.draw.rect(screen, (255, 255, 255), slot['rect'], 3)

        # Draw help text
        font = pygame.font.Font(None, 28)
        selected_text = f"Selected: {selected_info['name'].upper()}"
        help_text_str = f"EDITOR | {selected_text} | S: Save | P: Play | ESC: Menu"
        help_text = font.render(help_text_str, True, (255, 255, 255))
        pygame.draw.rect(screen, (0, 0, 0, 150), (0, SCREEN_HEIGHT - 30, SCREEN_WIDTH, 30))
        screen.blit(help_text, (10, SCREEN_HEIGHT - 28))

    elif game_state.current_state == GAME_STATE_PAUSED:
        # Draw game background (dimmed)
        screen.fill((30, 30, 50))

        # Draw level objects (dimmed)
        for obj in level_objects:
            info = obj['info']
            color = tuple(c // 2 for c in info['color'])  # Dim the colors
            if info['type'] == 'checkpoint' and obj.get('activated', False):
                color = tuple(c // 2 for c in info['activated_color'])
            elif info['type'] == 'collectible' and obj.get('collected', False):
                continue
            pygame.draw.rect(screen, color, camera.apply(obj['rect']))

        # Draw dimmed player
        dimmed_player_color = (127, 0, 0)
        pygame.draw.rect(screen, dimmed_player_color, camera.apply(player.rect))

        # Draw pause menu
        menu.draw_pause_menu(screen)

    pygame.display.flip()

pygame.quit()