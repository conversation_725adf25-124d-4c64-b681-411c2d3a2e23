import pygame
import heapq

# Barvy
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GREY = (200, 200, 200)
GREEN = (0, 255, 0)
RED = (255, 0, 0)
BLUE = (0, 0, 255)
DARK_RED = (150, 0, 0)

# Velikost okna a mřížky
WIDTH, HEIGHT = 600, 600
ROWS, COLS = 30, 30
CELL_SIZE = WIDTH // COLS

# Inicializace pygame
pygame.init()
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("2D Path Finder (A*)")
font = pygame.font.SysFont("consolas", 30)

# Mřížka: 0 = volno, 1 = překážka
grid = [[0 for _ in range(COLS)] for _ in range(ROWS)]
start = (0, 0)
goal = (ROWS - 1, COLS - 1)

def heuristic(a, b):
    return abs(a[0] - b[0]) + abs(a[1] - b[1])

def draw_grid():
    for x in range(0, WIDTH, CELL_SIZE):
        pygame.draw.line(screen, GREY, (x, 0), (x, HEIGHT))
    for y in range(0, HEIGHT, CELL_SIZE):
        pygame.draw.line(screen, GREY, (0, y), (WIDTH, y))

def draw_cells(path=[]):
    for i in range(ROWS):
        for j in range(COLS):
            color = WHITE
            if grid[i][j] == 1:
                color = BLACK
            pygame.draw.rect(screen, color, (j * CELL_SIZE, i * CELL_SIZE, CELL_SIZE, CELL_SIZE))
    
    # ✅ vykresli cestu jen pokud existuje
    if path:
        for x, y in path:
            pygame.draw.rect(screen, BLUE, (y * CELL_SIZE, x * CELL_SIZE, CELL_SIZE, CELL_SIZE))
    
    pygame.draw.rect(screen, GREEN, (start[1] * CELL_SIZE, start[0] * CELL_SIZE, CELL_SIZE, CELL_SIZE))
    pygame.draw.rect(screen, RED, (goal[1] * CELL_SIZE, goal[0] * CELL_SIZE, CELL_SIZE, CELL_SIZE))

def draw_message(msg, color):
    text = font.render(msg, True, color)
    screen.blit(text, (10, 10))

def astar(start, goal):
    open_set = [(0 + heuristic(start, goal), 0, start)]
    came_from = {}
    g_score = {start: 0}
    visited = set()

    while open_set:
        _, current_cost, current = heapq.heappop(open_set)

        if current == goal:
            path = []
            while current in came_from:
                path.append(current)
                current = came_from[current]
            path.append(start)
            return path[::-1]

        visited.add(current)

        for dx, dy in [(-1,0),(1,0),(0,-1),(0,1)]:
            nx, ny = current[0] + dx, current[1] + dy
            neighbor = (nx, ny)

            if 0 <= nx < ROWS and 0 <= ny < COLS and grid[nx][ny] == 0:
                tentative_g = g_score[current] + 1
                if neighbor not in g_score or tentative_g < g_score[neighbor]:
                    g_score[neighbor] = tentative_g
                    f_score = tentative_g + heuristic(neighbor, goal)
                    heapq.heappush(open_set, (f_score, tentative_g, neighbor))
                    came_from[neighbor] = current

    return None

def main():
    running = True
    path = []
    no_path = False

    while running:
        screen.fill(WHITE)
        draw_cells(path)
        draw_grid()
        if no_path:
            draw_message("No path found", DARK_RED)
        pygame.display.flip()

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False

            # Levé tlačítko = přidat překážku
            elif pygame.mouse.get_pressed()[0]:
                x, y = pygame.mouse.get_pos()
                row, col = y // CELL_SIZE, x // CELL_SIZE
                if (row, col) != start and (row, col) != goal:
                    grid[row][col] = 1

            # Pravé tlačítko = odebrat překážku
            elif pygame.mouse.get_pressed()[2]:
                x, y = pygame.mouse.get_pos()
                row, col = y // CELL_SIZE, x // CELL_SIZE
                if (row, col) != start and (row, col) != goal:
                    grid[row][col] = 0

            # Klávesy
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    path = astar(start, goal)
                    no_path = path is None
                elif event.key == pygame.K_r:
                    for i in range(ROWS):
                        for j in range(COLS):
                            grid[i][j] = 0
                    path = []
                    no_path = False

    pygame.quit()

main()
